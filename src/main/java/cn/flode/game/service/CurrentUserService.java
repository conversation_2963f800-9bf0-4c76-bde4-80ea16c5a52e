package cn.flode.game.service;

import cn.flode.game.entity.User;
import cn.flode.game.security.UserPrincipal;
import cn.flode.game.util.SecurityUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 当前用户服务类，演示如何使用SecurityUtils
 *
 * <AUTHOR>
 * @since 2025-06-18
 */
@Service
@RequiredArgsConstructor
public class CurrentUserService {

    private final UserService userService;

    /**
     * 获取当前登录用户的完整信息
     *
     * @return 用户实体，如果未登录返回null
     */
    public User getCurrentUserEntity() {
        Long currentUserId = SecurityUtils.currentUserId();
        if (currentUserId == null) {
            return null;
        }
        return userService.getById(currentUserId);
    }

    /**
     * 获取当前用户的主体信息
     *
     * @return UserPrincipal，如果未登录返回null
     */
    public UserPrincipal getCurrentUserPrincipal() {
        return SecurityUtils.getCurrentUser();
    }

    /**
     * 检查当前用户是否有权限访问指定用户的资源
     *
     * @param targetUserId 目标用户ID
     * @return true表示有权限，false表示无权限
     */
    public boolean hasPermissionToAccess(Long targetUserId) {
        // 只有当前用户本人才能访问自己的资源
        return SecurityUtils.isCurrentUser(targetUserId);
    }

    /**
     * 获取当前用户的显示名称
     *
     * @return 显示名称，格式为 "昵称(ID: userId)"
     */
    public String getCurrentUserDisplayName() {
        if (!SecurityUtils.isAuthenticated()) {
            return "未登录用户";
        }

        Long userId = SecurityUtils.currentUserId();
        String nickName = SecurityUtils.currentNickName();
        return String.format("%s(ID: %d)", nickName, userId);
    }

    /**
     * 验证当前用户身份
     *
     * @throws RuntimeException 如果用户未登录
     */
    public void requireAuthentication() {
        if (!SecurityUtils.isAuthenticated()) {
            throw new RuntimeException("用户未登录，请先登录");
        }
    }

    /**
     * 验证当前用户是否为指定用户
     *
     * @param userId 用户ID
     * @throws RuntimeException 如果不是指定用户
     */
    public void requireCurrentUser(Long userId) {
        requireAuthentication();
        if (!SecurityUtils.isCurrentUser(userId)) {
            throw new RuntimeException("无权限访问其他用户的资源");
        }
    }
}
