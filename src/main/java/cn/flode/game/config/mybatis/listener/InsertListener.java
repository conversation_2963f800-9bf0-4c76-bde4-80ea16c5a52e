package cn.flode.game.config.mybatis.listener;

import cn.flode.game.framework.BaseEntity;
import cn.flode.game.security.UserPrincipal;
import cn.flode.game.util.SecurityUtils;
import com.mybatisflex.annotation.AbstractInsertListener;
import java.time.LocalDateTime;

public class InsertListener extends AbstractInsertListener<BaseEntity> {

  @Override
  public void doInsert(BaseEntity baseEntity) {
    baseEntity.setCreator(
        SecurityUtils.currentUserOptional().map(UserPrincipal::getUserId).orElse(null));
    baseEntity.setGmtCreate(LocalDateTime.now());
  }
}
