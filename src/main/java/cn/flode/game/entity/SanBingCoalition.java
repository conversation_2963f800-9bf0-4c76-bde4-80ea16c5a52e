package cn.flode.game.entity;

import cn.flode.game.framework.BaseEntity;
import com.mybatisflex.annotation.Table;
import java.io.Serial;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 实体类。
 *
 * <AUTHOR>
 * @since 2025-05-22
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Table("san_bing_coalition")
public class SanBingCoalition extends BaseEntity implements Serializable {

  @Serial private static final long serialVersionUID = 1L;

  /** 区编码，必须大于1 */
  private Integer areaCode;

  /** 管理人员ID（用户ID） */
  private Long managerId;

  /** 联盟名 */
  private String name;

  /** 联盟编码 */
  private String code;
}
