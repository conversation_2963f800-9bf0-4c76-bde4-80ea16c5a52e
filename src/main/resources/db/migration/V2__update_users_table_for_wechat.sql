-- 更新用户表以支持微信小程序登录
-- 添加微信小程序相关字段

-- 添加微信小程序openId字段（必需）
ALTER TABLE users ADD COLUMN open_id VARCHAR(255);

-- 添加微信unionId字段（可选）
ALTER TABLE users ADD COLUMN union_id VARCHAR(255);

-- 添加用户昵称字段
ALTER TABLE users ADD COLUMN nick_name VARCHAR(255);

-- 添加用户头像URL字段
ALTER TABLE users ADD COLUMN avatar_url VARCHAR(500);

-- 添加用户性别字段：0-未知，1-男，2-女
ALTER TABLE users ADD COLUMN gender INTEGER DEFAULT 0;

-- 添加用户所在国家字段
ALTER TABLE users ADD COLUMN country VARCHAR(100);

-- 添加用户所在省份字段
ALTER TABLE users ADD COLUMN province VARCHAR(100);

-- 添加用户所在城市字段
ALTER TABLE users ADD COLUMN city VARCHAR(100);

-- 添加用户语言字段
ALTER TABLE users ADD COLUMN language VARCHAR(50);

-- 为openId创建唯一索引
CREATE UNIQUE INDEX idx_users_open_id ON users(open_id) WHERE open_id IS NOT NULL;

-- 为unionId创建索引
CREATE INDEX idx_users_union_id ON users(union_id) WHERE union_id IS NOT NULL;

-- 将原有的username和password字段设为可空（因为微信小程序用户不需要这些字段）
ALTER TABLE users ALTER COLUMN username DROP NOT NULL;
ALTER TABLE users ALTER COLUMN password DROP NOT NULL;

-- 添加注释
COMMENT ON COLUMN users.open_id IS '微信小程序openId';
COMMENT ON COLUMN users.union_id IS '微信unionId';
COMMENT ON COLUMN users.nick_name IS '用户昵称';
COMMENT ON COLUMN users.avatar_url IS '用户头像URL';
COMMENT ON COLUMN users.gender IS '用户性别：0-未知，1-男，2-女';
COMMENT ON COLUMN users.country IS '用户所在国家';
COMMENT ON COLUMN users.province IS '用户所在省份';
COMMENT ON COLUMN users.city IS '用户所在城市';
COMMENT ON COLUMN users.language IS '用户语言';
