package cn.flode.game.service;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.api.WxMaUserService;
import cn.binarywang.wx.miniapp.bean.WxMaJscode2SessionResult;
import cn.flode.game.controller.dto.WeChatLoginDTO;
import cn.flode.game.controller.vo.LoginResult;
import cn.flode.game.entity.User;
import cn.flode.game.mapper.UserMapper;
import com.mybatisflex.core.query.QueryWrapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpSession;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 用户服务测试类
 *
 * <AUTHOR>
 * @since 2025-08-04
 */
@ExtendWith(MockitoExtension.class)
class UserServiceTest {

    @Mock
    private UserMapper userMapper;

    @Mock
    private WxMaService wxMaService;

    @Mock
    private WxMaUserService wxMaUserService;

    @Mock
    private ServletRequestAttributes requestAttributes;

    @Mock
    private HttpServletRequest request;

    @Mock
    private HttpSession session;

    @InjectMocks
    private UserService userService;

    private WeChatLoginDTO loginDTO;
    private User user;
    private WxMaJscode2SessionResult sessionResult;

    @BeforeEach
    void setUp() {
        loginDTO = new WeChatLoginDTO();
        loginDTO.setCode("test-code");
        loginDTO.setNickName("测试用户");
        loginDTO.setAvatarUrl("https://example.com/avatar.jpg");
        loginDTO.setGender(1);
        loginDTO.setCountry("China");
        loginDTO.setProvince("Guangdong");
        loginDTO.setCity("Shenzhen");
        loginDTO.setLanguage("zh_CN");

        user = User.builder()
                .openId("test-openid")
                .nickName("测试用户")
                .avatarUrl("https://example.com/avatar.jpg")
                .gender(1)
                .country("China")
                .province("Guangdong")
                .city("Shenzhen")
                .language("zh_CN")
                .build();
        user.setId(1L);

        sessionResult = new WxMaJscode2SessionResult();
        sessionResult.setOpenid("test-openid");
        sessionResult.setSessionKey("test-session-key");
    }

    @Test
    void testWeChatLoginNewUser() throws Exception {
        // Given
        when(wxMaService.getUserService()).thenReturn(wxMaUserService);
        when(wxMaUserService.getSessionInfo("test-code")).thenReturn(sessionResult);
        when(userMapper.selectOneByQuery(any(QueryWrapper.class))).thenReturn(null);
        when(userMapper.insert(any(User.class))).thenReturn(1);
        
        RequestContextHolder.setRequestAttributes(requestAttributes);
        when(requestAttributes.getRequest()).thenReturn(request);
        when(request.getSession(true)).thenReturn(session);

        // When
        LoginResult result = userService.weChatLogin(loginDTO);

        // Then
        assertNotNull(result);
        assertEquals("测试用户", result.getNickName());
        assertEquals("https://example.com/avatar.jpg", result.getAvatarUrl());
        assertTrue(result.getIsNewUser());
        verify(userMapper).insert(any(User.class));
        verify(session).setAttribute("userId", any(Long.class));
    }

    @Test
    void testWeChatLoginExistingUser() throws Exception {
        // Given
        when(wxMaService.getUserService()).thenReturn(wxMaUserService);
        when(wxMaUserService.getSessionInfo("test-code")).thenReturn(sessionResult);
        when(userMapper.selectOneByQuery(any(QueryWrapper.class))).thenReturn(user);
        when(userMapper.update(any(User.class))).thenReturn(1);
        
        RequestContextHolder.setRequestAttributes(requestAttributes);
        when(requestAttributes.getRequest()).thenReturn(request);
        when(request.getSession(true)).thenReturn(session);

        // When
        LoginResult result = userService.weChatLogin(loginDTO);

        // Then
        assertNotNull(result);
        assertEquals(1L, result.getUserId());
        assertEquals("测试用户", result.getNickName());
        assertEquals("https://example.com/avatar.jpg", result.getAvatarUrl());
        assertFalse(result.getIsNewUser());
        verify(userMapper).update(any(User.class));
        verify(session).setAttribute("userId", 1L);
    }

    @Test
    void testWeChatLoginInvalidCode() throws Exception {
        // Given
        when(wxMaService.getUserService()).thenReturn(wxMaUserService);
        when(wxMaUserService.getSessionInfo("test-code")).thenThrow(new RuntimeException("Invalid code"));

        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            userService.weChatLogin(loginDTO);
        });
        assertTrue(exception.getMessage().contains("登录失败"));
    }

    @Test
    void testGetUserByOpenId() {
        // Given
        when(userMapper.selectOneByQuery(any(QueryWrapper.class))).thenReturn(user);

        // When
        User result = userService.getUserByOpenId("test-openid");

        // Then
        assertNotNull(result);
        assertEquals("test-openid", result.getOpenId());
        assertEquals(1L, result.getId());
    }

    @Test
    void testLogout() {
        // Given
        RequestContextHolder.setRequestAttributes(requestAttributes);
        when(requestAttributes.getRequest()).thenReturn(request);
        when(request.getSession(false)).thenReturn(session);

        // When
        String result = userService.logout();

        // Then
        assertEquals("登出成功", result);
        verify(session).invalidate();
    }
}
