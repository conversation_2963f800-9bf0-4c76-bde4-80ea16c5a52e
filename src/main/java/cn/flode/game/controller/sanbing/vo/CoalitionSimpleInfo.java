package cn.flode.game.controller.sanbing.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "联盟简要信息")
public class CoalitionSimpleInfo {

    @Schema(description = "联盟ID", example = "123456")
    private Long coalitionId;
    
    @Schema(description = "联盟名称", example = "无敌联盟")
    private String name;
    
    @Schema(description = "联盟代码", example = "ABC123")
    private String code;

}
