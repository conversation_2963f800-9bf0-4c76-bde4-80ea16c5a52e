package cn.flode.game.controller.sanbing.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "操作结果")
public class OperationResult {

    @Schema(description = "操作是否成功", example = "true")
    private Boolean success;
    
    @Schema(description = "结果消息", example = "操作成功")
    private String message;
    
    public static OperationResult success(String message) {
        return new OperationResult(true, message);
    }
    
    public static OperationResult failure(String message) {
        return new OperationResult(false, message);
    }
}
