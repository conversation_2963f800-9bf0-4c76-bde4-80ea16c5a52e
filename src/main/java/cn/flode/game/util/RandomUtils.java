package cn.flode.game.util;

import java.util.concurrent.ThreadLocalRandom;

public class RandomUtils {

  private static final String characters =
      "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
  private static final ThreadLocalRandom random = ThreadLocalRandom.current();

  public static String randomString(int length) {
    StringBuilder sb = new StringBuilder(length);
    for (int i = 0; i < length; i++) {
      int randomIndex = random.nextInt(characters.length());
      char randomChar = characters.charAt(randomIndex);
      sb.append(randomChar);
    }

    return sb.toString();
  }
}
