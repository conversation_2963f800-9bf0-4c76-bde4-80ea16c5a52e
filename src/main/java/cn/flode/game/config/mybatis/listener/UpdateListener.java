package cn.flode.game.config.mybatis.listener;

import cn.flode.game.framework.BaseEntity;
import cn.flode.game.security.UserPrincipal;
import cn.flode.game.util.SecurityUtils;
import com.mybatisflex.annotation.AbstractUpdateListener;
import java.time.LocalDateTime;

public class UpdateListener extends AbstractUpdateListener<BaseEntity> {
  @Override
  public void doUpdate(BaseEntity baseEntity) {
    baseEntity.setUpdater(
        SecurityUtils.currentUserOptional().map(UserPrincipal::getUserId).orElse(null));
    baseEntity.setGmtUpdate(LocalDateTime.now());
  }
}
