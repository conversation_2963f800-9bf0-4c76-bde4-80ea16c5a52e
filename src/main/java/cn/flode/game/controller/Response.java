package cn.flode.game.controller;

import lombok.Getter;

@Getter
public class Response<T> {

  private final boolean success;

  private final String message;

  private final T data;

  private Response(boolean success, String message, T data) {
    this.success = success;
    this.message = message;
    this.data = data;
  }

  public static <T> Response<T> ok(T data) {
    return new Response<>(true, "成功", data);
  }

  public static <T> Response<T> fail(String message) {
    return new Response<>(false, message, null);
  }
}
