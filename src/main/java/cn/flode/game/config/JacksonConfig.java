package cn.flode.game.config;

import cn.flode.game.config.jackson.LongToStringSerializer;
import cn.flode.game.config.jackson.StringToLongDeserializer;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.module.SimpleModule;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.http.converter.json.Jackson2ObjectMapperBuilder;

/**
 * Jackson配置类
 * 配置Long类型的序列化和反序列化
 * 
 * <AUTHOR>
 * @since 2025-07-27
 */
@Configuration
public class JacksonConfig {

    /**
     * 配置ObjectMapper，将Long类型序列化为String，反序列化时支持String转Long
     */
    @Bean
    @Primary
    public ObjectMapper objectMapper(Jackson2ObjectMapperBuilder builder) {
        ObjectMapper objectMapper = builder.build();
        
        // 创建自定义模块
        SimpleModule longModule = new SimpleModule("LongModule");
        
        // 注册Long类型的序列化器和反序列化器
        longModule.addSerializer(Long.class, new LongToStringSerializer());
        longModule.addSerializer(Long.TYPE, new LongToStringSerializer());
        longModule.addDeserializer(Long.class, new StringToLongDeserializer());
        longModule.addDeserializer(Long.TYPE, new StringToLongDeserializer());
        
        // 注册模块
        objectMapper.registerModule(longModule);
        
        return objectMapper;
    }
}
