package cn.flode.game.controller.sanbing.dto;

import cn.flode.game.enums.sanbing.GroupType;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
@Schema(description = "创建联盟分组请求")
public class GroupCreateDTO {

    @Schema(description = "联盟ID", example = "123456", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "联盟ID不能为空")
    private Long coalitionId;

    @Schema(description = "分组名称", example = "攻城组1", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "分组名称不能为空")
    private String name;

    @Schema(description = "分组类型", example = "GONG_CHENG", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "分组类型不能为空")
    private GroupType type;

    @Schema(description = "分组任务", example = "负责攻城战斗")
    private String task;
}
