package cn.flode.game.entity;

import cn.flode.game.framework.BaseEntity;
import com.mybatisflex.annotation.Table;
import java.io.Serializable;

import java.io.Serial;
import java.time.LocalDateTime;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.EqualsAndHashCode;

/**
 * 用户实体类。
 *
 * <AUTHOR>
 * @since 2025-06-18
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Table("users")
public class User extends BaseEntity implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 微信小程序openId
     */
    private String openId;

    /**
     * 微信unionId（可选）
     */
    private String unionId;

    /**
     * 用户昵称
     */
    private String nickName;

    /**
     * 用户头像URL
     */
    private String avatarUrl;

    /**
     * 用户性别：0-未知，1-男，2-女
     */
    private Integer gender;

    /**
     * 用户所在国家
     */
    private String country;

    /**
     * 用户所在省份
     */
    private String province;

    /**
     * 用户所在城市
     */
    private String city;

    /**
     * 用户语言
     */
    private String language;

    /**
     * 最后登录时间
     */
    private LocalDateTime lastLoginTime;
}
