package cn.flode.game.config.mybatis.handler;

import com.mybatisflex.core.handler.JacksonTypeHandler;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import org.apache.ibatis.type.JdbcType;
import org.postgresql.util.PGobject;

public class JsonbTypeHand<PERSON> extends JacksonTypeHandler {
  public JsonbTypeHandler(Class<?> propertyType) {
    super(propertyType);
  }

  public JsonbTypeHandler(Class<?> propertyType, Class<?> genericType) {
    super(propertyType, genericType);
  }

  @Override
  public void setNonNullParameter(PreparedStatement ps, int i, Object parameter, JdbcType jdbcType)
      throws SQLException {
    PGobject jsonObject = new PGobject();
    jsonObject.setType("jsonb");
    try {
      jsonObject.setValue(super.toJson(parameter));
    } catch (Exception e) {
      throw new SQLException("Error converting MyObject to JSON", e);
    }
    ps.setObject(i, jsonObject);
  }
}
