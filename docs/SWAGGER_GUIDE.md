# Swagger API 文档使用指南

## 概述

本项目集成了Swagger（OpenAPI 3.0）来提供交互式的API文档。通过Swagger UI，您可以查看所有API接口、测试接口功能、查看请求和响应示例。

## 访问方式

启动应用后，可以通过以下地址访问Swagger文档：

- **Swagger UI**: http://localhost:8080/swagger-ui.html
- **OpenAPI JSON**: http://localhost:8080/v3/api-docs

## 功能特性

### 1. 完整的API文档
- 所有接口的详细说明
- 请求参数和响应格式
- 示例数据
- 错误码说明

### 2. 在线测试
- 直接在浏览器中测试API
- 支持JWT Token认证
- 实时查看请求和响应

### 3. 模型定义
- 完整的数据模型文档
- 字段类型和验证规则
- 示例值

## 使用步骤

### 1. 访问Swagger UI
打开浏览器，访问 http://localhost:8080/swagger-ui.html

### 2. 用户认证
对于需要认证的接口，需要先获取JWT Token：

1. **注册用户**（如果没有账号）：
   - 找到 "用户管理" -> "POST /user/register"
   - 点击 "Try it out"
   - 输入用户名和密码
   - 点击 "Execute"

2. **用户登录**：
   - 找到 "用户管理" -> "POST /user/login"
   - 点击 "Try it out"
   - 输入用户名和密码
   - 点击 "Execute"
   - 复制响应中的 `token` 值

3. **设置认证**：
   - 点击页面右上角的 "Authorize" 按钮
   - 在弹出框中输入：`Bearer YOUR_TOKEN`（注意Bearer后面有空格）
   - 点击 "Authorize"
   - 点击 "Close"

### 3. 测试API接口
认证设置完成后，就可以测试需要认证的接口了：

1. 选择要测试的接口
2. 点击 "Try it out"
3. 填写必要的参数
4. 点击 "Execute"
5. 查看响应结果

## API分组说明

### 用户管理
- `POST /user/register` - 用户注册（无需认证）
- `POST /user/login` - 用户登录（无需认证）
- `GET /user/info` - 获取当前用户信息（需要认证）
- `GET /user/profile` - 获取用户详细信息（需要认证）

### 三冰联盟管理
所有接口都需要JWT认证：
- `POST /sanBing/coalition` - 创建联盟
- `POST /sanBing/member` - 添加联盟成员
- `PUT /sanBing/member` - 更新联盟成员信息（待实现）
- `DELETE /sanBing/member` - 移除联盟成员（待实现）
- `POST /sanBing/group` - 创建分组（待实现）
- `PUT /sanBing/group` - 更新分组信息（待实现）
- `DELETE /sanBing/group` - 删除分组（待实现）

## 请求示例

### 用户注册
```json
{
  "username": "testuser",
  "password": "123456"
}
```

### 用户登录
```json
{
  "username": "testuser",
  "password": "123456"
}
```

### 创建联盟
```json
{
  "areaCode": 1,
  "name": "测试联盟"
}
```

### 添加联盟成员
```json
{
  "coalitionCode": "ABC123",
  "name": "张三",
  "level": 3,
  "combatPower": 100000,
  "addition": 1.5,
  "gatheringCapacity": 200
}
```

## 响应格式

所有API响应都遵循统一格式：

### 成功响应
```json
{
  "success": true,
  "message": "成功",
  "data": {
    // 具体数据
  }
}
```

### 错误响应
```json
{
  "success": false,
  "message": "错误信息",
  "data": null
}
```

## 常见状态码

- `200` - 请求成功
- `400` - 参数验证失败
- `401` - 未认证或Token无效
- `403` - 权限不足
- `404` - 资源不存在
- `500` - 服务器内部错误

## 开发者注意事项

### 1. 添加新接口的Swagger注解

```java
@Operation(summary = "接口摘要", description = "接口详细描述")
@ApiResponses(value = {
    @ApiResponse(responseCode = "200", description = "成功",
        content = @Content(schema = @Schema(implementation = ResultClass.class))),
    @ApiResponse(responseCode = "400", description = "参数错误")
})
@SecurityRequirement(name = "bearerAuth") // 需要认证的接口
@PostMapping("/example")
public ResultClass example(@Parameter(description = "参数描述") @RequestBody RequestClass request) {
    // 实现
}
```

### 2. 为DTO/VO类添加Schema注解

```java
@Data
@Schema(description = "请求/响应描述")
public class ExampleDTO {
    
    @Schema(description = "字段描述", example = "示例值", requiredMode = Schema.RequiredMode.REQUIRED)
    private String field;
}
```

### 3. 配置说明

Swagger配置在以下文件中：
- `SwaggerConfig.java` - 主要配置
- `SecurityConfig.java` - 安全配置（允许访问Swagger资源）
- `application.yaml` - Swagger UI配置

## 故障排除

### 1. 无法访问Swagger UI
- 检查应用是否正常启动
- 确认端口8080没有被占用
- 检查防火墙设置

### 2. 认证失败
- 确保Token格式正确：`Bearer <token>`
- 检查Token是否过期
- 重新登录获取新Token

### 3. 接口测试失败
- 检查请求参数是否正确
- 查看控制台错误日志
- 确认数据库连接正常

## 生产环境注意事项

在生产环境中，建议：
1. 禁用Swagger UI或限制访问权限
2. 不要暴露敏感信息
3. 定期更新API文档

可以通过以下配置禁用Swagger：
```yaml
springdoc:
  api-docs:
    enabled: false
  swagger-ui:
    enabled: false
```
