package cn.flode.game.controller;

import cn.flode.game.controller.dto.UserLoginDTO;
import cn.flode.game.controller.dto.UserRegisterDTO;
import cn.flode.game.controller.vo.LoginResult;
import cn.flode.game.controller.vo.RegisterResult;
import cn.flode.game.controller.vo.UserInfo;
import cn.flode.game.service.UserService;
import cn.flode.game.util.SecurityUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 用户控制器
 *
 * <AUTHOR>
 * @since 2025-06-18
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/user")
public class UserController {

  private final UserService userService;

  /**
   * 用户注册
   *
   * @param registerDTO 注册信息
   * @return 注册结果
   */
  @Operation(summary = "用户注册", description = "创建新用户账号")
  @PostMapping("/register")
  public RegisterResult register(
      @Parameter(description = "用户注册信息", required = true) @Validated @RequestBody
          UserRegisterDTO registerDTO) {
    return userService.register(registerDTO);
  }

  /**
   * 用户登录
   *
   * @param loginDTO 登录信息
   * @return 登录结果
   */
  @Operation(summary = "用户登录", description = "用户登录获取JWT Token")
  @PostMapping("/login")
  public LoginResult login(
      @Parameter(description = "用户登录信息", required = true) @Validated @RequestBody
          UserLoginDTO loginDTO) {
    return userService.login(loginDTO);
  }

  /**
   * 获取当前用户信息
   *
   * @return 用户信息
   */
  @Operation(summary = "获取当前用户信息", description = "获取当前登录用户的基本信息")
  @SecurityRequirement(name = "bearerAuth")
  @GetMapping("/info")
  public UserInfo getUserInfo() {
    Long userId = SecurityUtils.currentUserId();
    String username = SecurityUtils.currentUsername();
    return UserInfo.of(userId, username);
  }

  /**
   * 获取用户详细信息（别名接口）
   *
   * @return 用户信息
   */
  @Operation(summary = "获取用户详细信息", description = "获取当前登录用户的详细信息（与/info接口相同）")
  @SecurityRequirement(name = "bearerAuth")
  @GetMapping("/profile")
  public UserInfo getUserProfile() {
    return getUserInfo();
  }
}
