package cn.flode.game.controller.sanbing.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "创建账号结果")
public class AccountCreateResult {

    @Schema(description = "账号ID", example = "123456")
    private Long accountId;

    @Schema(description = "账号姓名", example = "张三")
    private String name;

    @Schema(description = "区域代码", example = "1")
    private Integer areaCode;

    @Schema(description = "结果消息", example = "账号创建成功")
    private String message;

    public static AccountCreateResult success(Long accountId, String name, Integer areaCode) {
        return new AccountCreateResult(accountId, name, areaCode, "账号创建成功");
    }
}
