package cn.flode.game.util;

import cn.flode.game.security.UserPrincipal;
import java.util.Optional;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;

/**
 * 安全工具类，用于获取当前登录用户信息
 *
 * <AUTHOR>
 * @since 2025-06-18
 */
public class SecurityUtils {

  /**
   * 获取当前登录的用户主体信息
   *
   * @return UserPrincipal 用户主体信息，如果未登录抛出异常
   */
  public static UserPrincipal getCurrentUser() {
    return currentUserOptional().orElseThrow(() -> new RuntimeException("未登录，请登录后重新操作"));
  }

  /**
   * 获取当前登录的用户主体信息
   *
   * @return UserPrincipal 用户主体信息，如果未登录抛出异常
   */
  public static Optional<UserPrincipal> currentUserOptional() {
    Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
    if (authentication != null && authentication.getPrincipal() instanceof UserPrincipal) {
      return Optional.of((UserPrincipal) authentication.getPrincipal());
    }
    return Optional.empty();
  }

  /**
   * 获取当前登录用户的ID
   *
   * @return 用户ID，如果未登录抛出异常
   */
  public static Long currentUserId() {
    return getCurrentUser().getUserId();
  }

  /**
   * 获取当前登录用户的用户名
   *
   * @return 用户名，如果未登录抛出异常
   */
  public static String currentUsername() {
    return getCurrentUser().getUsername();
  }

  /**
   * 检查当前用户是否已登录
   *
   * @return true表示已登录，false表示未登录
   */
  public static boolean isAuthenticated() {
    Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
    return authentication != null
        && authentication.isAuthenticated()
        && authentication.getPrincipal() instanceof UserPrincipal;
  }

  /**
   * 检查当前用户是否为指定用户
   *
   * @param userId 用户ID
   * @return true表示是指定用户，false表示不是
   */
  public static boolean isCurrentUser(Long userId) {
    Long currentUserId = currentUserId();
    return currentUserId.equals(userId);
  }

  /**
   * 获取当前用户的认证信息
   *
   * @return Authentication 认证信息，如果未登录抛出异常
   */
  public static Authentication getCurrentAuthentication() {
    return SecurityContextHolder.getContext().getAuthentication();
  }
}
