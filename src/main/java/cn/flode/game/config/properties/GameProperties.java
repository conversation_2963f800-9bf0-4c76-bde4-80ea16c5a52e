package cn.flode.game.config.properties;

import lombok.Data;
import lombok.RequiredArgsConstructor;
import org.springframework.boot.context.properties.ConfigurationProperties;

@Data
@RequiredArgsConstructor
@ConfigurationProperties(prefix = "game")
public class GameProperties {

    private final JwtProperties jwt = new JwtProperties();

    @Data
    public static class JwtProperties {

        private String secret = "djsai#sjDDfiw#24";

        private String expiration = "P7D";
    }
}
