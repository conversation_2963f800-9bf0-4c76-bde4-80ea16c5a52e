# Spring Security + JWT 使用示例

## 概述

本项目使用Spring Security + JWT的认证方案，支持SecurityContextHolder，可以在任何地方获取当前登录用户信息。

## 1. SecurityUtils工具类

### 基本用法

```java
@Service
public class SomeBusinessService {
    
    public void someBusinessMethod() {
        // 获取当前用户ID
        Long userId = SecurityUtils.getCurrentUserId();
        
        // 获取当前用户名
        String username = SecurityUtils.getCurrentUsername();
        
        // 获取当前用户完整信息
        UserPrincipal currentUser = SecurityUtils.getCurrentUser();
        
        // 检查是否已登录
        if (SecurityUtils.isAuthenticated()) {
            // 用户已登录的逻辑
        }
        
        // 检查是否为指定用户
        if (SecurityUtils.isCurrentUser(targetUserId)) {
            // 是当前用户的逻辑
        }
    }
}
```

### 在Controller中使用

```java
@RestController
@RequestMapping("/api")
public class SomeController {
    
    @GetMapping("/my-data")
    public ResponseEntity<String> getMyData() {
        Long currentUserId = SecurityUtils.getCurrentUserId();
        if (currentUserId == null) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body("未登录");
        }
        
        return ResponseEntity.ok("当前用户ID: " + currentUserId);
    }
    
    @PostMapping("/user/{userId}/action")
    public ResponseEntity<String> performAction(@PathVariable Long userId) {
        // 验证是否为当前用户
        if (!SecurityUtils.isCurrentUser(userId)) {
            return ResponseEntity.status(HttpStatus.FORBIDDEN).body("无权限");
        }
        
        // 执行操作
        return ResponseEntity.ok("操作成功");
    }
}
```

### 在Service中使用

```java
@Service
@RequiredArgsConstructor
public class UserDataService {
    
    private final UserRepository userRepository;
    
    public UserData getCurrentUserData() {
        Long userId = SecurityUtils.getCurrentUserId();
        if (userId == null) {
            throw new RuntimeException("用户未登录");
        }
        
        return userRepository.findByUserId(userId);
    }
    
    public void updateUserData(Long userId, UserData data) {
        // 权限检查
        if (!SecurityUtils.isCurrentUser(userId)) {
            throw new RuntimeException("无权限修改其他用户数据");
        }
        
        userRepository.updateUserData(userId, data);
    }
}
```

## 2. CurrentUserService示例服务

项目提供了一个示例服务类，展示了更多高级用法：

```java
@Service
@RequiredArgsConstructor
public class CurrentUserService {
    
    // 获取当前用户完整实体
    public User getCurrentUserEntity() {
        Long currentUserId = SecurityUtils.getCurrentUserId();
        return userService.getById(currentUserId);
    }
    
    // 权限检查
    public boolean hasPermissionToAccess(Long targetUserId) {
        return SecurityUtils.isCurrentUser(targetUserId);
    }
    
    // 强制要求登录
    public void requireAuthentication() {
        if (!SecurityUtils.isAuthenticated()) {
            throw new RuntimeException("用户未登录，请先登录");
        }
    }
    
    // 强制要求是指定用户
    public void requireCurrentUser(Long userId) {
        requireAuthentication();
        if (!SecurityUtils.isCurrentUser(userId)) {
            throw new RuntimeException("无权限访问其他用户的资源");
        }
    }
}
```

## 3. 在联盟系统中的应用

```java
@Service
public class CoalitionService {
    
    public Coalition createCoalition(CoalitionCreateDTO dto) {
        // 自动设置创建者为当前用户
        Long creatorId = SecurityUtils.getCurrentUserId();
        
        Coalition coalition = Coalition.builder()
                .name(dto.getName())
                .areaCode(dto.getAreaCode())
                .creatorId(creatorId)  // 自动设置创建者
                .build();
                
        return save(coalition);
    }
    
    public void deleteCoalition(Long coalitionId) {
        Coalition coalition = getById(coalitionId);
        
        // 只有创建者才能删除联盟
        if (!SecurityUtils.isCurrentUser(coalition.getCreatorId())) {
            throw new RuntimeException("只有联盟创建者才能删除联盟");
        }
        
        removeById(coalitionId);
    }
}
```

## 4. 自定义注解进行权限控制

可以创建自定义注解来简化权限控制：

```java
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface RequireLogin {
}

@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface RequireOwner {
    String userIdParam() default "userId";
}
```

然后使用AOP来处理：

```java
@Aspect
@Component
public class SecurityAspect {
    
    @Before("@annotation(requireLogin)")
    public void checkLogin(RequireLogin requireLogin) {
        if (!SecurityUtils.isAuthenticated()) {
            throw new RuntimeException("需要登录");
        }
    }
    
    @Before("@annotation(requireOwner) && args(.., userId, ..)")
    public void checkOwner(RequireOwner requireOwner, Long userId) {
        if (!SecurityUtils.isCurrentUser(userId)) {
            throw new RuntimeException("无权限访问");
        }
    }
}
```

使用示例：

```java
@RestController
public class UserController {
    
    @RequireLogin
    @GetMapping("/profile")
    public UserProfile getProfile() {
        // 方法执行前会自动检查登录状态
        return getCurrentUserProfile();
    }
    
    @RequireOwner
    @PutMapping("/user/{userId}/profile")
    public void updateProfile(@PathVariable Long userId, @RequestBody UserProfile profile) {
        // 方法执行前会自动检查是否为资源所有者
        updateUserProfile(userId, profile);
    }
}
```

## 5. 测试中的使用

在单元测试中模拟认证状态：

```java
@Test
void testWithAuthentication() {
    // 创建用户主体
    UserPrincipal userPrincipal = new UserPrincipal(1L, "testuser", "password");
    
    // 设置认证状态
    UsernamePasswordAuthenticationToken authentication = 
            new UsernamePasswordAuthenticationToken(userPrincipal, null, userPrincipal.getAuthorities());
    SecurityContextHolder.getContext().setAuthentication(authentication);
    
    // 现在SecurityUtils.getCurrentUserId()会返回1L
    assertEquals(1L, SecurityUtils.getCurrentUserId());
    
    // 测试完成后清理
    SecurityContextHolder.clearContext();
}
```

## 6. 最佳实践

1. **在Service层进行权限检查**：不要只在Controller层检查权限
2. **使用SecurityUtils而不是直接使用SecurityContextHolder**：更简洁且类型安全
3. **及时处理未登录情况**：检查SecurityUtils返回的null值
4. **在测试中清理SecurityContext**：避免测试间的状态污染
5. **记录安全相关的操作**：便于审计和调试

## 7. 常见问题

### Q: SecurityUtils.getCurrentUserId()返回null
A: 用户未登录或Token无效，检查请求头中的Authorization字段。

### Q: 在异步方法中无法获取用户信息
A: Spring Security的上下文默认不会传递到异步线程，需要配置SecurityContextHolder的策略。

### Q: 单元测试中SecurityUtils返回null
A: 需要在测试中手动设置SecurityContext，参考上面的测试示例。

## 8. 安全注意事项

1. **永远不要信任前端传递的用户ID**：始终使用SecurityUtils.getCurrentUserId()
2. **在关键操作前进行权限检查**：不要仅依赖前端的权限控制
3. **记录敏感操作的日志**：包含用户ID和操作时间
4. **定期检查Token的有效性**：避免使用过期或被撤销的Token
