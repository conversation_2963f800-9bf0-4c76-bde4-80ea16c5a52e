package cn.flode.game.framework;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import java.time.LocalDateTime;

import com.mybatisflex.core.keygen.KeyGenerators;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class BaseEntity {

  /** 主键ID */
  @Id(keyType = KeyType.Generator, value = KeyGenerators.snowFlakeId)
  private Long id;

  /** 创建者 */
  private Long creator;

  /** 创建时间 */
  private LocalDateTime gmtCreate;

  /** 更新时间 */
  private LocalDateTime gmtUpdate;

  /** 更新者 */
  private Long updater;

  /** 软删除 */
  @Column(isLogicDelete = true)
  private int deleted;
}
