package cn.flode.game.service;

import cn.flode.game.controller.sanbing.dto.AccountAddToGroupDTO;
import cn.flode.game.entity.SanBingCoalition;
import cn.flode.game.entity.SanBingGroup;
import cn.flode.game.entity.SanBingGroupAccount;
import cn.flode.game.entity.SanBingAccount;
import cn.flode.game.enums.sanbing.ApproveStatus;
import cn.flode.game.enums.sanbing.GroupType;
import cn.flode.game.mapper.SanBingApplyJoinGroupMapper;
import cn.flode.game.mapper.SanBingGroupAccountMapper;
import cn.flode.game.mapper.SanBingGroupMapper;
import cn.flode.game.util.SecurityUtils;
import com.mybatisflex.core.query.QueryWrapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class SanBingGroupAccountServiceTest {

    @Mock
    private SanBingGroupAccountMapper mapper;

    @Mock
    private SanBingCoalitionService coalitionService;

    @Mock
    private SanBingAccountService accountService;

    @Mock
    private SanBingGroupMapper groupMapper;

    @Mock
    private SanBingApplyJoinGroupMapper applyJoinGroupMapper;

    @InjectMocks
    private SanBingGroupAccountService groupAccountService;

    private SanBingCoalition coalition;
    private SanBingGroup guanDu1Group;
    private SanBingGroup guanDu2Group;
    private SanBingAccount account;
    private AccountAddToGroupDTO addDTO;

    @BeforeEach
    void setUp() {
        coalition = SanBingCoalition.builder()
            .managerId(100L)
            .name("测试联盟")
            .build();
        coalition.setId(1L);

        guanDu1Group = SanBingGroup.builder()
            .coalitionId(1L)
            .type(GroupType.GUAN_DU1)
            .name("官渡1组")
            .build();
        guanDu1Group.setId(10L);

        guanDu2Group = SanBingGroup.builder()
            .coalitionId(1L)
            .type(GroupType.GUAN_DU2)
            .name("官渡2组")
            .build();
        guanDu2Group.setId(20L);

        account = SanBingAccount.builder()
            .coalitionId(1L)
            .status(ApproveStatus.APPROVED)
            .name("测试账号")
            .build();
        account.setId(200L);

        addDTO = new AccountAddToGroupDTO();
        addDTO.setGroupId(20L); // 添加到GUAN_DU2
        addDTO.setAccountId(200L);
    }

    @Test
    void testAddAccountToGroup_GuanDuAutoMove() {
        try (MockedStatic<SecurityUtils> mockedSecurityUtils = mockStatic(SecurityUtils.class)) {
            // 模拟当前用户是联盟管理者
            mockedSecurityUtils.when(SecurityUtils::currentUserId).thenReturn(100L);

            // 模拟分组存在
            when(groupMapper.selectOneById(20L)).thenReturn(guanDu2Group);

            // 模拟联盟存在
            when(coalitionService.getById(1L)).thenReturn(coalition);

            // 模拟账号存在且已通过审核
            when(accountService.getById(200L)).thenReturn(account);

            // 模拟账号不在目标分组中
            when(mapper.selectCountByQuery(any(QueryWrapper.class))).thenReturn(0L);

            // 模拟账号已在GUAN_DU1分组中
            SanBingGroupAccount existingAccount = SanBingGroupAccount.builder()
                .groupId(10L)
                .accountId(200L)
                .build();
            when(mapper.selectListByQuery(any(QueryWrapper.class)))
                .thenReturn(Arrays.asList(existingAccount));

            // 模拟现有分组信息
            when(groupMapper.selectOneById(10L)).thenReturn(guanDu1Group);

            // 执行测试
            assertDoesNotThrow(() -> groupAccountService.addAccountToGroup(addDTO));

            // 验证删除操作被调用（从GUAN_DU1移除）
            verify(mapper, times(1)).deleteByQuery(any(QueryWrapper.class));

            // 验证保存操作被调用（添加到GUAN_DU2）
            verify(groupAccountService, times(1)).save(any(SanBingGroupAccount.class));
        }
    }

    @Test
    void testAddAccountToGroup_NoExistingGuanDu() {
        try (MockedStatic<SecurityUtils> mockedSecurityUtils = mockStatic(SecurityUtils.class)) {
            // 模拟当前用户是联盟管理者
            mockedSecurityUtils.when(SecurityUtils::currentUserId).thenReturn(100L);

            // 模拟分组存在
            when(groupMapper.selectOneById(20L)).thenReturn(guanDu2Group);

            // 模拟联盟存在
            when(coalitionService.getById(1L)).thenReturn(coalition);

            // 模拟账号存在且已通过审核
            when(accountService.getById(200L)).thenReturn(account);

            // 模拟账号不在目标分组中
            when(mapper.selectCountByQuery(any(QueryWrapper.class))).thenReturn(0L);

            // 模拟账号不在任何GUAN_DU分组中
            when(mapper.selectListByQuery(any(QueryWrapper.class)))
                .thenReturn(Collections.emptyList());

            // 执行测试
            assertDoesNotThrow(() -> groupAccountService.addAccountToGroup(addDTO));

            // 验证保存操作被调用（直接添加到GUAN_DU2）
            verify(groupAccountService, times(1)).save(any(SanBingGroupAccount.class));
        }
    }
}
