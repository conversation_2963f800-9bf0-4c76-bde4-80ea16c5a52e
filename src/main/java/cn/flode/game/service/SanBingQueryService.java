package cn.flode.game.service;

import cn.flode.game.controller.sanbing.vo.CoalitionDetailInfo;
import cn.flode.game.controller.sanbing.vo.GroupInfo;
import cn.flode.game.controller.sanbing.vo.AccountInfo;
import cn.flode.game.entity.SanBingCoalition;
import cn.flode.game.entity.SanBingGroup;
import cn.flode.game.entity.SanBingGroupAccount;
import cn.flode.game.entity.SanBingAccount;
import cn.flode.game.entity.SanBingApplyJoinGroup;
import cn.flode.game.enums.sanbing.ApproveStatus;
import cn.flode.game.enums.sanbing.GroupType;
import cn.flode.game.mapper.SanBingGroupAccountMapper;
import cn.flode.game.mapper.SanBingApplyJoinGroupMapper;
import cn.flode.game.util.SecurityUtils;
import com.mybatisflex.core.query.QueryWrapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 三冰查询服务，处理复杂的查询逻辑
 *
 * <AUTHOR>
 * @since 2025-05-22
 */
@Service
@RequiredArgsConstructor
public class SanBingQueryService {

    private final SanBingCoalitionService coalitionService;
    private final SanBingAccountService accountService;
    private final SanBingGroupService groupService;
    private final SanBingGroupAccountMapper groupAccountMapper;
    private final SanBingApplyJoinGroupMapper applyJoinGroupMapper;

    /**
     * 查询联盟详细信息
     */
    public CoalitionDetailInfo getCoalitionDetail(Long coalitionId) {
        Long currentUserId = SecurityUtils.currentUserId();
        
        SanBingCoalition coalition = coalitionService.getById(coalitionId);
        if (coalition == null) {
            throw new RuntimeException("联盟不存在");
        }
        
        boolean isManager = currentUserId.equals(coalition.getManagerId());
        
        // 构建基本信息
        CoalitionDetailInfo.CoalitionDetailInfoBuilder builder = CoalitionDetailInfo.builder()
            .coalitionId(coalition.getId())
            .name(coalition.getName())
            .code(coalition.getCode())
            .areaCode(coalition.getAreaCode())
            .managerId(coalition.getManagerId())
            .isManager(isManager);
        
        // 查询所有分组
        List<SanBingGroup> groups = groupService.list(
            QueryWrapper.create().eq(SanBingGroup::getCoalitionId, coalitionId)
        );
        
        // 按类型分组
        Map<GroupType, List<GroupInfo>> groupsByType = new HashMap<>();
        for (GroupType type : GroupType.values()) {
            groupsByType.put(type, new ArrayList<>());
        }
        
        // 填充分组信息
        for (SanBingGroup group : groups) {
            GroupInfo groupInfo = convertToGroupInfo(group);
            groupsByType.get(group.getType()).add(groupInfo);
        }
        
        builder.groupsByType(groupsByType);
        
        // 如果是管理员，添加待审核账号信息
        if (isManager) {
            List<SanBingAccount> pendingAccounts = accountService.list(
                QueryWrapper.create()
                    .eq(SanBingAccount::getCoalitionId, coalitionId)
                    .eq(SanBingAccount::getStatus, ApproveStatus.TO_BE_APPROVED)
            );

            List<AccountInfo> pendingAccountInfos = pendingAccounts.stream()
                .map(this::convertToAccountInfo)
                .collect(Collectors.toList());

            builder.pendingAccounts(pendingAccountInfos);

            // 查询申请加入分组类型的账号信息
            List<SanBingApplyJoinGroup> applyJoinGroups = applyJoinGroupMapper.selectListByQuery(
                QueryWrapper.create().eq(SanBingApplyJoinGroup::getCoalitionId, coalitionId)
            );

            // 按分组类型分组申请信息
            Map<GroupType, List<AccountInfo>> applyAccountsByType = new HashMap<>();
            for (GroupType type : GroupType.values()) {
                applyAccountsByType.put(type, new ArrayList<>());
            }

            for (SanBingApplyJoinGroup apply : applyJoinGroups) {
                SanBingAccount account = accountService.getById(apply.getAccountId());
                if (account != null && account.getStatus() == ApproveStatus.APPROVED) {
                    AccountInfo accountInfo = convertToAccountInfo(account);
                    applyAccountsByType.get(apply.getType()).add(accountInfo);
                }
            }

            builder.applyAccountsByType(applyAccountsByType);
        }
        
        return builder.build();
    }

    /**
     * 转换分组实体为VO
     */
    private GroupInfo convertToGroupInfo(SanBingGroup group) {
        // 查询分组账号
        List<SanBingGroupAccount> groupAccounts = groupAccountMapper.selectListByQuery(
            QueryWrapper.create().eq(SanBingGroupAccount::getGroupId, group.getId())
        );

        List<AccountInfo> accountInfos = new ArrayList<>();
        for (SanBingGroupAccount groupAccount : groupAccounts) {
            SanBingAccount account = accountService.getById(groupAccount.getAccountId());
            if (account != null) {
                accountInfos.add(convertToAccountInfo(account));
            }
        }

        return GroupInfo.builder()
            .groupId(group.getId())
            .name(group.getName())
            .type(group.getType())
            .task(group.getTask())
            .accounts(accountInfos)
            .build();
    }

    /**
     * 转换账号实体为VO
     */
    private AccountInfo convertToAccountInfo(SanBingAccount account) {
        return AccountInfo.builder()
            .accountId(account.getId())
            .name(account.getName())
            .level(account.getLevel())
            .combatPower(account.getCombatPower())
            .addition(account.getAddition())
            .gatheringCapacity(account.getGatheringCapacity())
            .status(account.getStatus())
            .build();
    }
}
