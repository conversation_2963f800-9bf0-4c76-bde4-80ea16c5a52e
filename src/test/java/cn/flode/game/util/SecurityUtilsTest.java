package cn.flode.game.util;

import cn.flode.game.security.UserPrincipal;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;

import static org.junit.jupiter.api.Assertions.*;

/**
 * SecurityUtils测试类
 *
 * <AUTHOR>
 * @since 2025-06-18
 */
class SecurityUtilsTest {

    private UserPrincipal userPrincipal;

    @BeforeEach
    void setUp() {
        userPrincipal = new UserPrincipal(1L, "测试用户", "test-openid");
    }

    @AfterEach
    void tearDown() {
        SecurityContextHolder.clearContext();
    }

    @Test
    void testGetCurrentUserWhenAuthenticated() {
        // Given
        UsernamePasswordAuthenticationToken authentication = 
                new UsernamePasswordAuthenticationToken(userPrincipal, null, userPrincipal.getAuthorities());
        SecurityContextHolder.getContext().setAuthentication(authentication);

        // When
        UserPrincipal currentUser = SecurityUtils.getCurrentUser();

        // Then
        assertNotNull(currentUser);
        assertEquals(1L, currentUser.getUserId());
        assertEquals("test-openid", currentUser.getUsername());
    }

    @Test
    void testGetCurrentUserWhenNotAuthenticated() {
        // Given - no authentication set

        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            SecurityUtils.getCurrentUser();
        });
        assertEquals("未登录，请登录后重新操作", exception.getMessage());
    }

    @Test
    void testCurrentUserId() {
        // Given
        UsernamePasswordAuthenticationToken authentication = 
                new UsernamePasswordAuthenticationToken(userPrincipal, null, userPrincipal.getAuthorities());
        SecurityContextHolder.getContext().setAuthentication(authentication);

        // When
        Long userId = SecurityUtils.currentUserId();

        // Then
        assertEquals(1L, userId);
    }

    @Test
    void testCurrentUserIdWhenNotAuthenticated() {
        // Given - no authentication set

        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            SecurityUtils.currentUserId();
        });
        assertEquals("未登录，请登录后重新操作", exception.getMessage());
    }

    @Test
    void testCurrentNickName() {
        // Given
        UsernamePasswordAuthenticationToken authentication =
                new UsernamePasswordAuthenticationToken(userPrincipal, null, userPrincipal.getAuthorities());
        SecurityContextHolder.getContext().setAuthentication(authentication);

        // When
        String nickName = SecurityUtils.currentNickName();

        // Then
        assertEquals("测试用户", nickName);
    }

    @Test
    void testCurrentOpenId() {
        // Given
        UsernamePasswordAuthenticationToken authentication =
                new UsernamePasswordAuthenticationToken(userPrincipal, null, userPrincipal.getAuthorities());
        SecurityContextHolder.getContext().setAuthentication(authentication);

        // When
        String openId = SecurityUtils.currentOpenId();

        // Then
        assertEquals("test-openid", openId);
    }

    @Test
    void testIsAuthenticatedWhenAuthenticated() {
        // Given
        UsernamePasswordAuthenticationToken authentication = 
                new UsernamePasswordAuthenticationToken(userPrincipal, null, userPrincipal.getAuthorities());
        SecurityContextHolder.getContext().setAuthentication(authentication);

        // When
        boolean isAuthenticated = SecurityUtils.isAuthenticated();

        // Then
        assertTrue(isAuthenticated);
    }

    @Test
    void testIsAuthenticatedWhenNotAuthenticated() {
        // Given - no authentication set

        // When
        boolean isAuthenticated = SecurityUtils.isAuthenticated();

        // Then
        assertFalse(isAuthenticated);
    }

    @Test
    void testIsCurrentUserWhenMatches() {
        // Given
        UsernamePasswordAuthenticationToken authentication = 
                new UsernamePasswordAuthenticationToken(userPrincipal, null, userPrincipal.getAuthorities());
        SecurityContextHolder.getContext().setAuthentication(authentication);

        // When
        boolean isCurrentUser = SecurityUtils.isCurrentUser(1L);

        // Then
        assertTrue(isCurrentUser);
    }

    @Test
    void testIsCurrentUserWhenNotMatches() {
        // Given
        UsernamePasswordAuthenticationToken authentication = 
                new UsernamePasswordAuthenticationToken(userPrincipal, null, userPrincipal.getAuthorities());
        SecurityContextHolder.getContext().setAuthentication(authentication);

        // When
        boolean isCurrentUser = SecurityUtils.isCurrentUser(2L);

        // Then
        assertFalse(isCurrentUser);
    }

    @Test
    void testIsCurrentUserWhenNotAuthenticated() {
        // Given - no authentication set

        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            SecurityUtils.isCurrentUser(1L);
        });
        assertEquals("未登录，请登录后重新操作", exception.getMessage());
    }
}
