package cn.flode.game.controller.sanbing.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class AccountAddResult {

    private Long accountId;

    private String message;

    public static AccountAddResult success(Long accountId) {
        return new AccountAddResult(accountId, "账号添加成功，状态为待审核");
    }
}
