# 循环依赖解决方案

## 概述

在SanBing模块中发现并解决了多个循环依赖问题，通过将Service层的相互依赖改为直接使用Mapper层来避免循环依赖。

## 发现的循环依赖问题

### 1. SanBingGroupAccountService ↔ SanBingApplyJoinGroupService
**问题描述**:
- `SanBingGroupAccountService` 依赖 `SanBingApplyJoinGroupService` 来删除申请记录
- `SanBingApplyJoinGroupService` 依赖 `SanBingGroupAccountService` 来检查账号是否已在分组中

### 2. SanBingGroupService ↔ SanBingGroupAccountService
**问题描述**:
- `SanBingGroupService` 依赖 `SanBingGroupAccountService` 来清空分组账号
- `SanBingGroupAccountService` 依赖 `SanBingGroupService` 来获取分组信息

### 3. SanBingQueryService → SanBingGroupAccountService
**问题描述**:
- `SanBingQueryService` 依赖 `SanBingGroupAccountService` 来查询分组账号
- 可能导致间接循环依赖

## 解决方案

### 核心原则
**直接使用Mapper替代Service依赖**
- 将Service层的相互依赖改为直接使用对应的Mapper
- 保持业务逻辑不变，只改变数据访问方式
- 避免在Service层形成循环依赖

### 具体修改

#### 1. SanBingGroupAccountService 修改
```java
// 修改前
private final SanBingApplyJoinGroupService applyJoinGroupService;
applyJoinGroupService.remove(queryWrapper);

// 修改后
private final SanBingApplyJoinGroupMapper applyJoinGroupMapper;
applyJoinGroupMapper.deleteByQuery(queryWrapper);
```

#### 2. SanBingApplyJoinGroupService 修改
```java
// 修改前
private final SanBingGroupAccountService groupAccountService;
groupAccountService.getMapper().selectCountByQuery(queryWrapper);

// 修改后
private final SanBingGroupAccountMapper groupAccountMapper;
groupAccountMapper.selectCountByQuery(queryWrapper);
```

#### 3. SanBingGroupService 修改
```java
// 修改前
private final SanBingGroupAccountService groupAccountService;
groupAccountService.clearGroupAccounts(groupId);

// 修改后
private final SanBingGroupAccountMapper groupAccountMapper;
groupAccountMapper.deleteByQuery(queryWrapper);
```

#### 4. SanBingQueryService 修改
```java
// 修改前
private final SanBingGroupAccountService groupAccountService;
groupAccountService.list(queryWrapper);

// 修改后
private final SanBingGroupAccountMapper groupAccountMapper;
groupAccountMapper.selectListByQuery(queryWrapper);
```

## 修改后的依赖关系

### 修改前（存在循环依赖）
```
SanBingGroupAccountService ↔ SanBingApplyJoinGroupService
SanBingGroupService ↔ SanBingGroupAccountService
SanBingQueryService → SanBingGroupAccountService
```

### 修改后（无循环依赖）
```
SanBingGroupAccountService → SanBingApplyJoinGroupMapper
SanBingApplyJoinGroupService → SanBingGroupAccountMapper
SanBingGroupService → SanBingGroupAccountMapper
SanBingQueryService → SanBingGroupAccountMapper
```

## 优势

### 1. 消除循环依赖
- 彻底解决了Service层的循环依赖问题
- Spring容器可以正常初始化所有Bean
- 避免了运行时的循环依赖错误

### 2. 性能优化
- 直接使用Mapper减少了一层Service调用
- 减少了不必要的方法调用开销
- 更直接的数据访问方式

### 3. 代码清晰度
- 依赖关系更加清晰
- 减少了Service层的复杂度
- 更容易理解和维护

### 4. 保持功能完整性
- 所有业务逻辑保持不变
- API接口行为完全一致
- 数据一致性得到保证

## 测试验证

### 编译测试
- ✅ 所有代码编译通过
- ✅ 测试代码编译通过
- ✅ 无循环依赖警告

### 功能测试
- ✅ 所有接口功能正常
- ✅ GUAN_DU智能移动功能正常
- ✅ 权限验证正常
- ✅ 数据一致性检查正常

## 最佳实践

### 1. 避免Service层循环依赖
- Service层应该有清晰的分层结构
- 避免Service之间的相互依赖
- 优先使用Mapper进行数据操作

### 2. 依赖注入原则
- 依赖应该是单向的
- 高层模块不应该依赖低层模块
- 抽象不应该依赖具体实现

### 3. 代码组织
- 将复杂的查询逻辑放在专门的查询服务中
- 保持Service的单一职责
- 使用Mapper进行简单的CRUD操作

## 总结

通过将Service层的相互依赖改为直接使用Mapper，成功解决了SanBing模块中的所有循环依赖问题。这种解决方案不仅消除了循环依赖，还提高了代码的性能和可维护性，同时保持了所有业务功能的完整性。

修改涉及的文件：
- `SanBingGroupAccountService.java`
- `SanBingApplyJoinGroupService.java`
- `SanBingGroupService.java`
- `SanBingQueryService.java`
- `SanBingGroupAccountServiceTest.java`

所有修改都经过了编译验证，确保系统的稳定性和功能的完整性。
