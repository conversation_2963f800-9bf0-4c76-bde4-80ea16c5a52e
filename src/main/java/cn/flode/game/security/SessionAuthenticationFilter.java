package cn.flode.game.security;

import cn.flode.game.entity.User;
import cn.flode.game.service.UserService;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.lang.NonNull;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;
import java.util.Arrays;
import java.util.List;

/**
 * Session认证过滤器
 *
 * <AUTHOR>
 * @since 2025-08-04
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class SessionAuthenticationFilter extends OncePerRequestFilter {

    private final UserService userService;
    private final AntPathMatcher pathMatcher = new AntPathMatcher();

    // 不需要认证的路径
    private final List<String> excludedPaths = Arrays.asList(
            "/error",
            "/user/wechat-login",
            "/user/logout",
            "/v3/api-docs/**",
            "/swagger-ui/**",
            "/swagger-ui.html"
    );

    @Override
    protected void doFilterInternal(@NonNull HttpServletRequest request,
                                    @NonNull HttpServletResponse response,
                                    @NonNull FilterChain filterChain) throws ServletException, IOException {

        String requestPath = request.getRequestURI();
        boolean shouldSkip = excludedPaths.stream()
                .anyMatch(pattern -> pathMatcher.match(pattern, requestPath));

        if (!shouldSkip) {
            try {
                HttpSession session = request.getSession(false);
                if (session != null) {
                    Long userId = (Long) session.getAttribute("userId");
                    if (userId != null) {
                        // 根据用户ID获取用户信息
                        User user = userService.getById(userId);
                        if (user != null) {
                            UserPrincipal userPrincipal = UserPrincipal.create(user);
                            UsernamePasswordAuthenticationToken authentication =
                                    new UsernamePasswordAuthenticationToken(
                                            userPrincipal, null, userPrincipal.getAuthorities());
                            authentication.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));

                            SecurityContextHolder.getContext().setAuthentication(authentication);
                        }
                    }
                }
            } catch (Exception ex) {
                log.error("Could not set user authentication in security context", ex);
            }
        }

        filterChain.doFilter(request, response);
    }
}
