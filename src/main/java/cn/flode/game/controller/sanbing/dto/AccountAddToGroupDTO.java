package cn.flode.game.controller.sanbing.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
@Schema(description = "将账号添加到分组请求")
public class AccountAddToGroupDTO {

    @Schema(description = "分组ID", example = "123456", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "分组ID不能为空")
    private Long groupId;

    @Schema(description = "账号ID", example = "789012", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "账号ID不能为空")
    private Long accountId;
}
