-- 创建用户表
CREATE TABLE IF NOT EXISTS users (
    id BIGINT PRIMARY KEY,
    username VARCHAR(20) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    creator BIGINT,
    gmt_create TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    gmt_update TIM<PERSON><PERSON>MP DEFAULT CURRENT_TIMESTAMP,
    updater BIGINT,
    deleted INTEGER DEFAULT 0
);

-- 创建用户名索引
CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);

-- 创建软删除索引
CREATE INDEX IF NOT EXISTS idx_users_deleted ON users(deleted);

-- 插入测试数据（可选）
-- 密码是 "123456" 的BCrypt加密结果
INSERT INTO users (id, username, password, gmt_create, gmt_update, deleted)
VALUES (1, 'admin', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBOsl7iKTVKIFi', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 0)
ON CONFLICT (id) DO NOTHING;

INSERT INTO users (id, username, password, gmt_create, gmt_update, deleted)
VALUES (2, 'testuser', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBOsl7iKTVKIFi', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 0)
ON CONFLICT (id) DO NOTHING;
