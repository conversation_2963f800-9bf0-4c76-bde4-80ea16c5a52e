package cn.flode.game.controller.sanbing.vo;

import cn.flode.game.enums.sanbing.ApproveStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "账号信息")
public class AccountInfo {

    @Schema(description = "账号ID", example = "123456")
    private Long accountId;

    @Schema(description = "账号姓名", example = "张三")
    private String name;

    @Schema(description = "等级", example = "3")
    private Integer level;

    @Schema(description = "战力", example = "100000")
    private Integer combatPower;

    @Schema(description = "加成", example = "1.5")
    private Double addition;

    @Schema(description = "集结容量", example = "200")
    private Integer gatheringCapacity;

    @Schema(description = "审核状态", example = "APPROVED")
    private ApproveStatus status;

    @Schema(description = "联盟信息")
    private CoalitionSimpleInfo coalition;
}
