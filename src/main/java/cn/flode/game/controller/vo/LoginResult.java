package cn.flode.game.controller.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 登录结果VO
 *
 * <AUTHOR>
 * @since 2025-06-18
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "登录结果")
public class LoginResult {

  /** 用户ID */
  @Schema(description = "用户ID", example = "1234567890")
  private Long userId;

  /** 用户昵称 */
  @Schema(description = "用户昵称", example = "微信用户")
  private String nickName;

  /** 用户头像URL */
  @Schema(description = "用户头像URL", example = "https://thirdwx.qlogo.cn/mmopen/...")
  private String avatarUrl;

  /** 是否为新用户 */
  @Schema(description = "是否为新用户", example = "false")
  private Boolean isNewUser;

  public static LoginResult success(Long userId, String nickName, String avatarUrl, Boolean isNewUser) {
    return new LoginResult(userId, nickName, avatarUrl, isNewUser);
  }
}
