package cn.flode.game.controller.sanbing.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
@Schema(description = "更新联盟分组请求")
public class GroupUpdateDTO {

    @Schema(description = "分组ID", example = "123456", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "分组ID不能为空")
    private Long groupId;

    @Schema(description = "分组名称", example = "攻城组1", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "分组名称不能为空")
    private String name;

    @Schema(description = "分组任务", example = "负责攻城战斗")
    private String task;
}
