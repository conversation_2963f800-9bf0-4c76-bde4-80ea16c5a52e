package cn.flode.game.controller.sanbing.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
@Schema(description = "创建账号请求")
public class AccountCreateDTO {

    @Schema(description = "区域代码", example = "1", minimum = "1", requiredMode = Schema.RequiredMode.REQUIRED)
    @Min(value = 1, message = "区域代码必须大于0")
    @NotNull(message = "区域代码不能为空")
    private Integer areaCode;

    @Schema(description = "账号姓名", example = "张三", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "账号姓名不能为空")
    private String name;

    @Schema(description = "战力", example = "100000", minimum = "0", requiredMode = Schema.RequiredMode.REQUIRED)
    @Min(value = 0, message = "战力不能为负数")
    @NotNull(message = "战力不能为空")
    private Integer combatPower;

    @Schema(description = "加成", example = "1.5", minimum = "0")
    @Min(value = 0, message = "加成不能为负数")
    private Double addition;

    @Schema(description = "集结容量", example = "200", minimum = "0")
    @Min(value = 0, message = "集结容量不能为负数")
    private Integer gatheringCapacity;
}
