package cn.flode.game.config;

import cn.flode.game.controller.Response;
import cn.flode.game.exception.BusinessException;
import cn.flode.game.util.JsonUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.MethodParameter;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.lang.NonNull;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;

@Slf4j
@RequiredArgsConstructor
@RestControllerAdvice(basePackages = "cn.flode.game")
public class ResponseAdvise implements ResponseBodyAdvice<Object> {

  @Override
  public boolean supports(
      @NonNull MethodParameter returnType,
      @NonNull Class<? extends HttpMessageConverter<?>> converterType) {
    Class<?> parameterType = returnType.getParameterType();
    return !parameterType.isAssignableFrom(Response.class);
  }

  @Override
  public Object beforeBodyWrite(
      Object body,
      @NonNull MethodParameter returnType,
      @NonNull MediaType selectedContentType,
      @NonNull Class<? extends HttpMessageConverter<?>> selectedConverterType,
      @NonNull ServerHttpRequest request,
      @NonNull ServerHttpResponse response) {
    if (body instanceof String) {
      response.getHeaders().add(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE);
      return JsonUtils.toString(Response.ok(body));
    }
    return Response.ok(body);
  }

  /**
   * 处理业务异常
   */
  @ExceptionHandler(BusinessException.class)
  public Response<Object> handleBusinessException(BusinessException exception) {
    log.warn("业务异常: {}", exception.getMessage());
    return Response.fail(exception.getMessage());
  }

  /**
   * 处理其他异常
   */
  @ExceptionHandler(Exception.class)
  public Response<Object> handleException(Exception exception) {
    log.error("服务器异常", exception);
    return Response.fail("服务器出错，联系网站管理员");
  }
}
