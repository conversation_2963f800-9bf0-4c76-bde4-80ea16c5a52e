# 项目启动指南

## 环境要求

- Java 21 或更高版本
- Maven 3.6+
- PostgreSQL 数据库
- Docker（可选，用于运行数据库）

## 1. 环境配置

### 检查Java版本
```bash
java -version
```
确保使用Java 21或更高版本。如果当前版本不符合要求，请：

1. 下载并安装Java 21
2. 设置JAVA_HOME环境变量
3. 更新PATH环境变量

### Windows环境设置JAVA_HOME示例
```cmd
set JAVA_HOME=C:\Program Files\Java\jdk-21
set PATH=%JAVA_HOME%\bin;%PATH%
```

## 2. 数据库设置

### 方式一：使用Docker（推荐）
```bash
# 启动PostgreSQL容器
docker-compose up -d

# 检查容器状态
docker-compose ps
```

### 方式二：手动安装PostgreSQL
1. 安装PostgreSQL
2. 创建数据库：`game`
3. 创建用户：`game`，密码：`KV753t0PpVmpjd2d`
4. 授予权限

### 初始化数据库表
连接到数据库后，执行以下SQL脚本：
```bash
# 使用psql连接数据库
psql -h localhost -U game -d game

# 执行建表脚本
\i src/main/resources/sql/create_users_table.sql
```

## 3. 项目编译和运行

### 编译项目
```bash
mvn clean compile
```

### 运行测试
```bash
mvn test
```

### 启动应用
```bash
mvn spring-boot:run
```

应用将在 http://localhost:8080 启动

## 4. 验证安装

### 方式一：使用Swagger UI（推荐）
1. 启动应用后访问：http://localhost:8080/swagger-ui.html
2. 在Swagger UI中测试所有接口
3. 详细使用方法请参考：[Swagger使用指南](SWAGGER_GUIDE.md)

### 方式二：使用curl命令

#### 测试用户注册
```bash
curl -X POST http://localhost:8080/user/register \
  -H "Content-Type: application/json" \
  -d '{
    "username": "newuser",
    "password": "123456"
  }'
```

#### 测试用户登录
```bash
curl -X POST http://localhost:8080/user/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "newuser",
    "password": "123456"
  }'
```

#### 测试认证接口
```bash
# 使用登录返回的token
curl -X GET http://localhost:8080/user/info \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

## 5. 常见问题

### Q: 编译时出现Java版本错误
A: 确保JAVA_HOME指向Java 21安装目录，并重启命令行工具。

### Q: 数据库连接失败
A: 检查PostgreSQL是否正在运行，端口5432是否可用。

### Q: Token验证失败
A: 确保在请求头中正确设置Authorization字段，格式为 "Bearer <token>"。

### Q: 用户名已存在错误
A: 检查数据库中是否已有同名用户，或使用不同的用户名。

## 6. 开发工具推荐

- **IDE**: IntelliJ IDEA 或 Eclipse
- **API测试**: Postman 或 Insomnia
- **数据库管理**: DBeaver 或 pgAdmin
- **版本控制**: Git

## 7. 项目结构

```
src/
├── main/
│   ├── java/cn/flode/game/
│   │   ├── controller/          # 控制器层
│   │   ├── service/             # 服务层
│   │   ├── entity/              # 实体类
│   │   ├── mapper/              # 数据访问层
│   │   ├── config/              # 配置类
│   │   ├── interceptor/         # 拦截器
│   │   └── util/                # 工具类
│   └── resources/
│       ├── mapper/              # MyBatis XML文件
│       ├── sql/                 # SQL脚本
│       └── application.yaml     # 配置文件
└── test/                        # 测试代码
```

## 8. 配置说明

### application.yaml主要配置
```yaml
spring:
  main:
    banner-mode: off

mybatis-flex:
  global-config:
    print-banner: off

jwt:
  secret: your-secret-key-here
  expiration: P7D  # 7天过期
```

### 数据库配置
数据库连接信息在 `compose.yaml` 中配置，如需修改请同步更新应用配置。

## 9. 下一步

项目启动成功后，您可以：

1. 查看API文档：`API_USAGE_EXAMPLES.md`
2. 运行单元测试验证功能
3. 使用Postman等工具测试API
4. 根据需要扩展业务功能

## 10. 技术支持

如遇到问题，请检查：
1. 日志输出
2. 数据库连接状态
3. 端口占用情况
4. 环境变量配置
