package cn.flode.game.controller.sanbing.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "区域账号信息")
public class AreaAccount {

    @Schema(description = "区域代码", example = "1")
    private Integer areaCode;

    @Schema(description = "该区域中用户创建的所有账号信息")
    private List<AccountInfo> accounts;
}
