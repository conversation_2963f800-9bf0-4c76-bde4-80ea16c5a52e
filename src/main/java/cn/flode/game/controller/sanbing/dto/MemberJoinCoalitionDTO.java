package cn.flode.game.controller.sanbing.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
@Schema(description = "账号申请加入联盟请求")
public class AccountJoinCoalitionDTO {

    @Schema(description = "账号ID", example = "123456", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "账号ID不能为空")
    private Long accountId;

    @Schema(description = "联盟代码", example = "ABC123", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "联盟代码不能为空")
    private String coalitionCode;
}
