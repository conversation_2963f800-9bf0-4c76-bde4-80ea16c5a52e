package cn.flode.game.util;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.SneakyThrows;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public final class JsonUtils {

  private static ObjectMapper objectMapper;

  private JsonUtils() {}

  @Autowired
  public void setObjectMapper(ObjectMapper objectMapper) {
    JsonUtils.objectMapper = objectMapper;
  }

  @SneakyThrows
  public static <T> String toString(T object) {
    return objectMapper.writeValueAsString(object);
  }
}
