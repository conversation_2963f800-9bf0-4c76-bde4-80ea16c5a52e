package cn.flode.game.controller.sanbing.vo;

import cn.flode.game.enums.sanbing.GroupType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "分组信息")
public class GroupInfo {

    @Schema(description = "分组ID", example = "123456")
    private Long groupId;
    
    @Schema(description = "分组名称", example = "攻城组1")
    private String name;
    
    @Schema(description = "分组类型", example = "GONG_CHENG")
    private GroupType type;
    
    @Schema(description = "分组任务", example = "负责攻城战斗")
    private String task;
    
    @Schema(description = "分组账号列表")
    private List<AccountInfo> accounts;
}
