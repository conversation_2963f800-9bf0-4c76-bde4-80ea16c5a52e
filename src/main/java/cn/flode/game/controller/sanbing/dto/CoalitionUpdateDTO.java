package cn.flode.game.controller.sanbing.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 更新联盟信息请求DTO
 * 
 * <AUTHOR>
 * @since 2025-07-27
 */
@Data
@Schema(description = "更新联盟信息请求")
public class CoalitionUpdateDTO {

    @Schema(description = "联盟ID", example = "123456", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "联盟ID不能为空")
    private Long coalitionId;

    @Schema(description = "联盟名称", example = "无敌联盟", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "联盟名称不能为空")
    private String name;

    @Schema(description = "联盟代码", example = "ABC123", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "联盟代码不能为空")
    private String code;
}
