package cn.flode.game.controller.sanbing;

import cn.flode.game.controller.sanbing.dto.CoalitionCreateDTO;
import cn.flode.game.controller.sanbing.dto.AccountCreateDTO;
import cn.flode.game.service.*;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@WebMvcTest(SanBingController.class)
class SanBingControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @MockBean
    private SanBingCoalitionService coalitionService;

    @MockBean
    private SanBingAccountService accountService;

    @MockBean
    private SanBingGroupService groupService;

    @MockBean
    private SanBingGroupAccountService groupAccountService;

    @MockBean
    private SanBingApplyJoinGroupService applyJoinGroupService;

    @MockBean
    private SanBingQueryService queryService;

    @Test
    void testCreateCoalitionEndpoint() throws Exception {
        CoalitionCreateDTO createDTO = new CoalitionCreateDTO();
        createDTO.setAreaCode(1);
        createDTO.setName("测试联盟");

        // 这个测试会因为没有认证而返回401，这是预期的
        mockMvc.perform(post("/sanBing/coalition")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(createDTO)))
                .andExpect(status().isUnauthorized());
    }

    @Test
    void testCreateAccountEndpoint() throws Exception {
        AccountCreateDTO createDTO = new AccountCreateDTO();
        createDTO.setAreaCode(1);
        createDTO.setName("测试账号");
        createDTO.setCombatPower(100000);
        createDTO.setAddition(1.5);
        createDTO.setGatheringCapacity(200);

        // 这个测试会因为没有认证而返回401，这是预期的
        mockMvc.perform(post("/sanBing/account")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(createDTO)))
                .andExpect(status().isUnauthorized());
    }


}
