package cn.flode.game.controller.sanbing.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 我的联盟信息VO
 * 
 * <AUTHOR>
 * @since 2025-07-27
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "我的联盟信息")
public class MyCoalitionInfo {

    @Schema(description = "联盟ID", example = "123456")
    private Long coalitionId;
    
    @Schema(description = "联盟名称", example = "无敌联盟")
    private String name;
    
    @Schema(description = "联盟代码", example = "ABC123")
    private String code;
    
    @Schema(description = "区号", example = "1")
    private Integer areaCode;
    
    @Schema(description = "联盟成员数量", example = "25")
    private Long memberCount;
    
    @Schema(description = "待审核成员数量", example = "3")
    private Long pendingMemberCount;
}
