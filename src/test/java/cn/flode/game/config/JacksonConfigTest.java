package cn.flode.game.config;

import cn.flode.game.config.jackson.LongToStringSerializer;
import cn.flode.game.config.jackson.StringToLongDeserializer;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.module.SimpleModule;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Jackson配置测试类
 *
 * <AUTHOR>
 * @since 2025-07-27
 */
class JacksonConfigTest {

    private ObjectMapper objectMapper;

    @BeforeEach
    void setUp() {
        objectMapper = new ObjectMapper();

        // 创建自定义模块
        SimpleModule longModule = new SimpleModule("LongModule");

        // 注册Long类型的序列化器和反序列化器
        longModule.addSerializer(Long.class, new LongToStringSerializer());
        longModule.addSerializer(Long.TYPE, new LongToStringSerializer());
        longModule.addDeserializer(Long.class, new StringToLongDeserializer());
        longModule.addDeserializer(Long.TYPE, new StringToLongDeserializer());

        // 注册模块
        objectMapper.registerModule(longModule);
    }

    /**
     * 测试Long序列化为String
     */
    @Test
    void testLongSerialization() throws Exception {
        TestObject testObject = new TestObject();
        testObject.setId(123456789L);
        testObject.setName("test");

        String json = objectMapper.writeValueAsString(testObject);
        
        // 验证Long被序列化为String
        assertTrue(json.contains("\"id\":\"123456789\""));
        assertTrue(json.contains("\"name\":\"test\""));
    }

    /**
     * 测试String反序列化为Long
     */
    @Test
    void testLongDeserialization() throws Exception {
        String json = "{\"id\":\"123456789\",\"name\":\"test\"}";
        
        TestObject testObject = objectMapper.readValue(json, TestObject.class);
        
        // 验证String被反序列化为Long
        assertEquals(123456789L, testObject.getId());
        assertEquals("test", testObject.getName());
    }

    /**
     * 测试null值处理
     */
    @Test
    void testNullHandling() throws Exception {
        TestObject testObject = new TestObject();
        testObject.setId(null);
        testObject.setName("test");

        String json = objectMapper.writeValueAsString(testObject);
        
        // 验证null值正确处理
        assertTrue(json.contains("\"id\":null"));
        
        TestObject deserializedObject = objectMapper.readValue(json, TestObject.class);
        assertNull(deserializedObject.getId());
        assertEquals("test", deserializedObject.getName());
    }

    /**
     * 测试用的简单对象
     */
    public static class TestObject {
        private Long id;
        private String name;

        public Long getId() {
            return id;
        }

        public void setId(Long id) {
            this.id = id;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }
    }
}
