package cn.flode.game.security;

import cn.flode.game.entity.User;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;

import java.util.Collection;
import java.util.Collections;

/**
 * 用户主体信息，实现Spring Security的UserDetails接口
 *
 * <AUTHOR>
 * @since 2025-06-18
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserPrincipal implements UserDetails {

    private Long userId;
    private String nickName;
    private String openId;

    /**
     * 从User实体创建UserPrincipal
     *
     * @param user 用户实体
     * @return UserPrincipal
     */
    public static UserPrincipal create(User user) {
        return new UserPrincipal(
                user.getId(),
                user.getNickName(),
                user.getOpenId()
        );
    }

    @Override
    public Collection<? extends GrantedAuthority> getAuthorities() {
        // 暂时返回空权限，后续可以根据需要扩展角色权限
        return Collections.emptyList();
    }

    @Override
    public String getPassword() {
        return null; // 微信小程序登录不需要密码
    }

    @Override
    public String getUsername() {
        return openId; // 使用openId作为用户名
    }

    @Override
    public boolean isAccountNonExpired() {
        return true;
    }

    @Override
    public boolean isAccountNonLocked() {
        return true;
    }

    @Override
    public boolean isCredentialsNonExpired() {
        return true;
    }

    @Override
    public boolean isEnabled() {
        return true;
    }
}
