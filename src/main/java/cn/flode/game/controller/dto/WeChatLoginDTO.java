package cn.flode.game.controller.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

/**
 * 微信小程序登录DTO
 *
 * <AUTHOR>
 * @since 2025-08-04
 */
@Data
@Schema(description = "微信小程序登录请求")
public class WeChatLoginDTO {

    @Schema(description = "微信小程序登录凭证code", example = "081234567890", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "登录凭证不能为空")
    private String code;

    @Schema(description = "用户昵称", example = "微信用户")
    private String nickName;

    @Schema(description = "用户头像URL", example = "https://thirdwx.qlogo.cn/mmopen/...")
    private String avatarUrl;

    @Schema(description = "用户性别：0-未知，1-男，2-女", example = "1")
    private Integer gender;

    @Schema(description = "用户所在国家", example = "China")
    private String country;

    @Schema(description = "用户所在省份", example = "Guangdong")
    private String province;

    @Schema(description = "用户所在城市", example = "Shenzhen")
    private String city;

    @Schema(description = "用户语言", example = "zh_CN")
    private String language;
}
