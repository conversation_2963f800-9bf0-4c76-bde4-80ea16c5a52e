# 微信小程序登录功能说明

## 概述

本项目已完成从JWT认证到微信小程序登录的改造，现在支持微信小程序用户通过微信授权进行登录。

## 主要变更

### 1. 用户体系改造
- **移除了传统的用户名/密码登录方式**
- **移除了JWT Token认证机制**
- **改为基于Session的认证方式**
- **用户表新增微信小程序相关字段**

### 2. 新增字段
用户表(`users`)新增以下字段：
- `open_id`: 微信小程序openId（必需，唯一）
- `union_id`: 微信unionId（可选）
- `nick_name`: 用户昵称
- `avatar_url`: 用户头像URL
- `gender`: 用户性别（0-未知，1-男，2-女）
- `country`: 用户所在国家
- `province`: 用户所在省份
- `city`: 用户所在城市
- `language`: 用户语言

### 3. API接口变更

#### 新增接口
- `POST /user/wechat-login` - 微信小程序登录
- `POST /user/logout` - 用户登出

#### 移除接口
- `POST /user/register` - 用户注册（不再需要）
- `POST /user/login` - 用户登录（不再需要）

## 配置说明

### 1. 微信小程序配置
在 `application.yaml` 中配置微信小程序信息：

```yaml
wechat:
  miniapp:
    app-id: your-miniapp-appid  # 替换为实际的小程序AppId
    secret: your-miniapp-secret  # 替换为实际的小程序Secret
```

### 2. 数据库迁移
运行数据库迁移脚本 `V2__update_users_table_for_wechat.sql` 来更新用户表结构。

## 使用方法

### 1. 微信小程序登录

**请求示例：**
```http
POST /user/wechat-login
Content-Type: application/json

{
  "code": "081234567890",
  "nickName": "微信用户",
  "avatarUrl": "https://thirdwx.qlogo.cn/mmopen/...",
  "gender": 1,
  "country": "China",
  "province": "Guangdong",
  "city": "Shenzhen",
  "language": "zh_CN"
}
```

**响应示例：**
```json
{
  "userId": 1,
  "nickName": "微信用户",
  "avatarUrl": "https://thirdwx.qlogo.cn/mmopen/...",
  "isNewUser": false
}
```

### 2. 获取用户信息

**请求示例：**
```http
GET /user/info
```

**响应示例：**
```json
{
  "userId": 1,
  "nickName": "微信用户"
}
```

### 3. 用户登出

**请求示例：**
```http
POST /user/logout
```

**响应示例：**
```
登出成功
```

## 认证机制

### Session认证
- 用户登录成功后，系统会在Session中存储用户ID
- 后续请求通过Session验证用户身份
- 登出时会清除Session信息

### 安全性
- 微信小程序的code具有时效性，确保登录安全
- Session机制提供了良好的状态管理
- openId作为用户唯一标识，确保用户身份的唯一性

## 开发注意事项

1. **微信小程序配置**：确保在微信公众平台正确配置小程序信息
2. **HTTPS要求**：生产环境需要使用HTTPS协议
3. **Session配置**：可根据需要调整Session超时时间
4. **错误处理**：注意处理微信API调用可能出现的异常情况

## 测试

运行测试用例：
```bash
mvn test
```

主要测试覆盖：
- 新用户微信登录
- 老用户微信登录
- 无效code处理
- 用户信息获取
- 用户登出功能
