package cn.flode.game.util;

import cn.flode.game.framework.BaseEntity;
import com.mybatisflex.codegen.Generator;
import com.mybatisflex.codegen.config.GlobalConfig;
import com.zaxxer.hikari.HikariDataSource;

public class GenCode {

  public static void main(String[] args) {
    HikariDataSource dataSource = new HikariDataSource();
    dataSource.setJdbcUrl("*************************************");
    dataSource.setUsername("game");
    dataSource.setPassword("KV753t0PpVmpjd2d");
    GlobalConfig globalConfig = createGlobalConfig();
    Generator generator = new Generator(dataSource, globalConfig);
    generator.generate();
  }

  public static GlobalConfig createGlobalConfig() {
    GlobalConfig globalConfig = new GlobalConfig();
    globalConfig
        .getPackageConfig()
        .setBasePackage("cn.flode.game")
        .setServiceImplPackage("cn.flode.game.service");
    globalConfig
        .enableEntity()
        .setSuperClass(BaseEntity.class)
        .setWithLombok(true)
        .setJdkVersion(21);
    globalConfig.getServiceImplConfig().setClassSuffix("Service");
    globalConfig.enableMapper().setMapperAnnotation(true);
    globalConfig.enableMapperXml();
    globalConfig.enableServiceImpl();
    return globalConfig;
  }
}
