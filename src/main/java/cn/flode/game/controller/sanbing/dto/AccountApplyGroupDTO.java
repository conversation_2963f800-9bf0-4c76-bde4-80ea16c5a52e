package cn.flode.game.controller.sanbing.dto;

import cn.flode.game.enums.sanbing.GroupType;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
@Schema(description = "账号申请加入分组类型请求")
public class AccountApplyGroupDTO {

    @Schema(description = "账号ID", example = "123456", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "账号ID不能为空")
    private Long accountId;

    @Schema(description = "分组类型", example = "GONG_CHENG", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "分组类型不能为空")
    private GroupType groupType;
}
