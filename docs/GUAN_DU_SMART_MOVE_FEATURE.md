# GUAN_DU分组智能移动功能

## 功能概述

在"添加成员到分组"接口中实现了智能移动功能。当联盟管理员将成员添加到GUAN_DU1或GUAN_DU2分组时，系统会自动检查该成员是否已在另一个GUAN_DU类型分组中，如果是，则自动从原分组移除，然后添加到新分组。

## 核心逻辑

### 智能移动规则
1. **GUAN_DU1 → GUAN_DU2**: 如果成员在GUAN_DU1分组中，添加到GUAN_DU2时会自动从GUAN_DU1移除
2. **GUAN_DU2 → GUAN_DU1**: 如果成员在GUAN_DU2分组中，添加到GUAN_DU1时会自动从GUAN_DU2移除
3. **其他分组类型**: 不受此规则影响，按正常逻辑处理

### 实现细节

#### 检查逻辑
```java
// 处理GUAN_DU类型的特殊逻辑（GUAN_DU1和GUAN_DU2是同一种类型）
if (group.getType() == GroupType.GUAN_DU1 || group.getType() == GroupType.GUAN_DU2) {
    // 查找账号是否已在其他GUAN_DU类型分组中
    List<SanBingGroupAccount> existingGuanDuAccounts = mapper.selectListByQuery(
        QueryWrapper.create()
            .eq(SanBingGroupAccount::getAccountId, addDTO.getAccountId())
            .in(SanBingGroup::getType, GroupType.GUAN_DU1, GroupType.GUAN_DU2)
            .leftJoin(SanBingGroup.class).on(SanBingGroupAccount::getGroupId, SanBingGroup::getId)
    );
    
    // 如果成员已在其他GUAN_DU类型分组中，先移除
    for (SanBingGroupMember existingMember : existingGuanDuMembers) {
        // 获取现有分组信息
        SanBingGroup existingGroup = groupService.getById(existingMember.getGroupId());
        if (existingGroup != null && !existingGroup.getId().equals(addDTO.getGroupId())) {
            // 确保现有分组也属于同一联盟
            if (existingGroup.getCoalitionId().equals(coalition.getId())) {
                // 从现有GUAN_DU分组中移除
                mapper.deleteByQuery(
                    QueryWrapper.create()
                        .eq(SanBingGroupMember::getGroupId, existingGroup.getId())
                        .eq(SanBingGroupMember::getMemberId, addDTO.getMemberId())
                );
            }
        }
    }
}
```

#### 安全检查
1. **联盟一致性**: 确保现有分组和目标分组属于同一联盟
2. **分组存在性**: 验证现有分组确实存在
3. **避免重复**: 不会从目标分组中移除（避免自己移除自己）

## 业务场景

### 使用场景
1. **战略调整**: 根据战斗需要重新分配账号位置
2. **负载均衡**: 平衡两个GUAN_DU分组的账号数量
3. **简化操作**: 管理员无需手动先移除再添加

### 操作示例

#### 场景1: 账号从GUAN_DU1移动到GUAN_DU2
```bash
# 假设账号123456当前在GUAN_DU1分组中
# 管理员想将其移动到GUAN_DU2分组

curl -X POST "http://localhost:8080/sanBing/group/add-account" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "groupId": 20,      // GUAN_DU2分组ID
    "accountId": 123456
  }'

# 结果：
# 1. 成员自动从GUAN_DU1分组中移除
# 2. 成员添加到GUAN_DU2分组中
# 3. 删除相关的GUAN_DU类型申请记录
```

#### 场景2: 成员首次加入GUAN_DU分组
```bash
# 假设成员789012当前不在任何GUAN_DU分组中
# 管理员将其添加到GUAN_DU1分组

curl -X POST "http://localhost:8080/sanBing/group/add-member" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "groupId": 10,      // GUAN_DU1分组ID
    "memberId": 789012
  }'

# 结果：
# 1. 账号直接添加到GUAN_DU1分组中
# 2. 删除相关的GUAN_DU类型申请记录
```

## 与原有功能的对比

### 之前的逻辑
- 如果账号已在其他GUAN_DU类型分组中，会抛出异常
- 管理员需要先手动移除，再添加到新分组

### 现在的逻辑
- 自动检测并处理GUAN_DU分组间的冲突
- 一步完成移动操作，提升用户体验

## 错误处理

### 保持的验证
- 权限验证：只有联盟管理者可操作
- 账号状态验证：账号必须已通过审核
- 联盟归属验证：账号必须属于该联盟
- 重复添加检查：不能添加到已存在的分组

### 新增的安全措施
- 联盟一致性检查：确保移除和添加在同一联盟内
- 分组存在性验证：确保要移除的分组确实存在

## 数据一致性

### 事务保证
- 移除和添加操作在同一个事务中执行
- 确保操作的原子性

### 申请记录清理
- 自动删除相关的GUAN_DU类型申请记录
- 避免数据冗余

## 性能优化

### 查询优化
- 使用LEFT JOIN减少数据库查询次数
- 只在GUAN_DU类型分组时执行额外检查

### 批量操作
- 一次查询获取所有相关的GUAN_DU分组成员记录
- 批量处理移除操作

## 测试覆盖

### 单元测试
- 测试智能移动功能
- 测试无现有分组的情况
- 测试权限验证
- 测试错误场景

### 集成测试
- 端到端的移动流程测试
- 数据一致性验证

## 总结

智能移动功能大大简化了GUAN_DU分组的管理操作，提升了用户体验。通过在现有的"添加账号到分组"接口中集成智能检测和自动移除逻辑，实现了无缝的账号移动功能，同时保持了数据的一致性和操作的安全性。
