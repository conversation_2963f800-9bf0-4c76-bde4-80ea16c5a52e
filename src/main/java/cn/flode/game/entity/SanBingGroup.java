package cn.flode.game.entity;

import cn.flode.game.enums.sanbing.GroupType;
import cn.flode.game.framework.BaseEntity;
import com.mybatisflex.annotation.Table;
import java.io.Serializable;

import java.io.Serial;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.EqualsAndHashCode;

/**
 *  联盟分组实体类。
 *
 * <AUTHOR>
 * @since 2025-05-22
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Table("san_bing_group")
public class SanBingGroup extends BaseEntity implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 联盟ID
     */
    private Long coalitionId;

    /**
     * 分组名称
     */
    private String name;

    /**
     * 分组类型
     */
    private GroupType type;

    /**
     * 分组任务
     */
    private String task;

}
