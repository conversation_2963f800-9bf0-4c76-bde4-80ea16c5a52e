package cn.flode.game.controller.sanbing.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
@Schema(description = "审核账号申请请求")
public class AccountApproveDTO {

    @Schema(description = "账号ID", example = "123456", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "账号ID不能为空")
    private Long accountId;

    @Schema(description = "是否通过审核", example = "true", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "审核结果不能为空")
    private Boolean approved;
}
